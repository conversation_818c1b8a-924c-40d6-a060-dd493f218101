#!/usr/bin/env python3
"""

Data Sources:
- working_vehicles.csv (matching vehicles)
- working_unique_vehicles.csv (unique vehicles)
- hv_repair_2025-06-02b.csv (repair events)
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date
import logging
import sys
from typing import Dict, List, Tuple, Optional, Set, Any, TypedDict
from collections import defaultdict
from helpers import BatteryProcessor, DataLoader, BatteryEvent, BatteryInterval
import warnings


warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("generate_battery_timeline.log"),
    ],
)
logger = logging.getLogger(__name__)


class VehicleDataManager:
    """
    Builds vehicle info, aggregates battery events from repair/working data,
    and provides activity queries. Handles data structuring and vehicle activity lookups.
    """

    def __init__(
        self,
        daily_stats_by_vehicle: Dict[int, pd.DataFrame],
        vin_to_vehicle_id: Dict[str, int],
    ):
        self.daily_stats_by_vehicle = daily_stats_by_vehicle
        self.vin_to_vehicle_id = vin_to_vehicle_id
        self.vin_without_vehicle_id = set()
        self.errors = []

    def build_vehicle_info(
        self, working_vehicles_df: pd.DataFrame, hv_repair_df: pd.DataFrame
    ) -> Dict[str, Dict[str, Any]]:
        """Build comprehensive vehicle information dictionary."""
        logger.info("Building vehicle information...")

        vehicle_info = {}

        # Process working vehicles data
        for _, row in working_vehicles_df.iterrows():
            vin = row["vin"]
            first_active_date = self.get_first_active_date_for_vin(vin)
            vehicle_info[vin] = {
                "vin": vin,
                "erstzulassung": (
                    row.get("erstzulassung").date()
                    if pd.notna(row.get("erstzulassung"))
                    else first_active_date
                ),
                "first_active_date": first_active_date,
                "master_battery": row.get("master"),
                "slave_battery": row.get("slave"),
                "akz": row.get("akz"),
                "last_active_date": self.get_last_active_date_for_vin(vin),
            }

        # Add vehicles from repair data that aren't in working data
        for _, row in hv_repair_df.iterrows():
            vin = row["vin"]
            if vin not in vehicle_info:
                first_active_date = self.get_first_active_date_for_vin(vin)
                vehicle_info[vin] = {
                    "vin": vin,
                    "erstzulassung": first_active_date,
                    "first_active_date": first_active_date,
                    "master_battery": None,
                    "slave_battery": None,
                    "akz": None,
                    "last_active_date": self.get_last_active_date_for_vin(vin),
                }

        logger.info(f"Built info for {len(vehicle_info)} vehicles")

        # Count dual-battery vehicles
        vehicles_with_both_batteries = sum(
            1
            for info in vehicle_info.values()
            if info["master_battery"] and info["slave_battery"]
        )
        logger.info(
            f"Dual-battery vehicles from working data: {vehicles_with_both_batteries}"
        )

        return vehicle_info

    def process_hv_repair_data(
        self, hv_repair_df: pd.DataFrame
    ) -> Tuple[Dict[str, List[Dict]], Set[str], Set[str]]:
        """Process HV repair data to track battery appearances and return unique sets."""
        logger.info("Processing HV repair data...")

        battery_vehicles = {}
        unique_batteries = set()
        unique_vehicles = set()

        # Sort by effective date to process chronologically
        hv_repair_sorted = hv_repair_df.sort_values("effective_date").reset_index(
            drop=True
        )

        for idx, row in hv_repair_sorted.iterrows():
            vin = row["vin"]
            effective_date = row["effective_date"].date()
            old_battery = row["battery_id_old"]
            new_battery = row["battery_id_new"]
            event_id = idx

            # Process old battery appearance
            if old_battery and pd.notna(old_battery):
                unique_batteries.add(old_battery)
                if old_battery not in battery_vehicles:
                    battery_vehicles[old_battery] = []
                battery_vehicles[old_battery].append(
                    {
                        "vin": vin,
                        "date": effective_date,
                        "column": "old",
                        "event_id": event_id,
                        "row_data": row,
                    }
                )
                logger.debug(
                    f"Battery {old_battery} appeared as old in vehicle {vin} on {effective_date}"
                )

            # Process new battery appearance
            if new_battery and pd.notna(new_battery):
                unique_batteries.add(new_battery)
                if new_battery not in battery_vehicles:
                    battery_vehicles[new_battery] = []
                battery_vehicles[new_battery].append(
                    {
                        "vin": vin,
                        "date": effective_date,
                        "column": "new",
                        "event_id": event_id,
                        "row_data": row,
                    }
                )
                logger.debug(
                    f"Battery {new_battery} appeared as new in vehicle {vin} on {effective_date}"
                )

            unique_vehicles.add(vin)

        logger.info(f"Found {len(unique_batteries)} batteries from repair data")
        return battery_vehicles, unique_batteries, unique_vehicles

    def add_vehicles_from_working_only_data(
        self,
        battery_vehicles: Dict[str, List[Dict]],
        vehicle_info: Dict[str, Dict[str, Any]],
        repair_vins: Set[str],
    ) -> Tuple[Dict[str, List[Dict]], Set[str], int]:
        """Add batteries from vehicles that only appear in working data."""
        logger.info("Adding batteries from working-only vehicles...")

        working_vins = set(vehicle_info.keys())
        working_only_vins = working_vins - repair_vins

        unique_batteries = set(battery_vehicles.keys())

        for vin in working_only_vins:
            self._add_working_only_batteries_for_vin(
                vin, vehicle_info[vin], battery_vehicles, unique_batteries
            )

        working_only_count = len(working_only_vins)
        logger.info(f"Added batteries from {working_only_count} working-only vehicles")

        return battery_vehicles, unique_batteries, working_only_count

    def _add_working_only_batteries_for_vin(
        self,
        vin: str,
        vehicle_info_entry: Dict[str, Any],
        battery_vehicles: Dict[str, List[Dict]],
        unique_batteries: Set[str],
    ) -> None:
        """Add batteries from a single working-only vehicle."""
        erstzulassung = vehicle_info_entry["erstzulassung"]

        if pd.isna(erstzulassung):
            logger.warning(f"Vehicle {vin} has no erstzulassung date")
            return

        # Add batteries from working data
        for battery_field in ["master_battery", "slave_battery"]:
            battery_id = vehicle_info_entry[battery_field]
            if battery_id and pd.notna(battery_id):
                unique_batteries.add(battery_id)
                if battery_id not in battery_vehicles:
                    battery_vehicles[battery_id] = []

                # Check for duplicates
                if not any(e["vin"] == vin for e in battery_vehicles[battery_id]):
                    battery_vehicles[battery_id].append(
                        {
                            "vin": vin,
                            "date": erstzulassung,
                            "column": None,
                            "event_id": None,
                            "row_data": None,
                        }
                    )
                    logger.info(
                        f"Added working-only battery {battery_id} for VIN {vin}"
                    )
                else:
                    logger.info(
                        f"Skipped duplicate: Battery {battery_id} already has repair events for VIN {vin}"
                    )

    def get_first_active_date_for_vin(self, vin: str) -> Optional[datetime.date]:
        """Get the first active date for a VIN from daily stats."""
        if vin not in self.vin_to_vehicle_id:
            self.vin_without_vehicle_id.add(vin)
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]
            if vehicle_id not in self.daily_stats_by_vehicle:
                logger.debug(
                    f"Cannot get first active date for {vin} and vehicle_id {vehicle_id} - no daily stats"
                )
                return None

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]
            if len(vehicle_data) > 0:
                first_date = vehicle_data["date"].min()
                if pd.notna(first_date):
                    return first_date.date()
            return None

        except Exception as e:
            logger.error(f"Error getting first active date for {vin}: {e}")
            return None

    def get_last_active_date_for_vin(self, vin: str) -> Optional[datetime.date]:
        """Get the last active date for a VIN from daily stats."""
        if vin not in self.vin_to_vehicle_id:
            self.vin_without_vehicle_id.add(vin)
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]
            if vehicle_id not in self.daily_stats_by_vehicle:
                logger.debug(
                    f"Cannot get last active date for {vin} and vehicle_id {vehicle_id} - no daily stats"
                )
                return None

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]
            if len(vehicle_data) > 0:
                last_date = vehicle_data["date"].max()
                if pd.notna(last_date):
                    return last_date.date()
            return None

        except Exception as e:
            logger.error(f"Error getting last active date for {vin}: {e}")
            return None

    def get_vehicle_activity_range(
        self, vin: str
    ) -> Tuple[Optional[datetime.date], Optional[datetime.date]]:
        """Get both first and last active dates for a VIN."""
        return self.get_first_active_date_for_vin(
            vin
        ), self.get_last_active_date_for_vin(vin)

    def vehicle_was_active(
        self, vin: str, start_date: datetime.date, end_date: datetime.date
    ) -> bool:
        """Check if vehicle drove during the specified date range."""
        vehicle_id = self.vin_to_vehicle_id.get(vin)
        if vehicle_id not in self.daily_stats_by_vehicle:
            return False

        stats = self.daily_stats_by_vehicle[vehicle_id]

        # Convert date objects to pandas Timestamps for comparison
        start_ts = pd.Timestamp(start_date) if start_date else None
        end_ts = pd.Timestamp(end_date) if end_date else None

        if start_ts is None or end_ts is None:
            return False

        window = stats[(stats["date"] >= start_ts) & (stats["date"] <= end_ts)]

        if window.empty:
            return False

        valid_records = window[
            (window["km_start"] >= 0)
            & (window["km_end"] >= 0)
            & (window["km_end"] >= window["km_start"])
        ]

        if valid_records.empty:
            return False

        total_km = (valid_records["km_end"] - valid_records["km_start"]).sum()
        return total_km > 0

    def get_unique_sets(
        self,
        battery_vehicles: Dict[str, List[Dict]],
        vehicle_info: Dict[str, Dict[str, Any]],
    ) -> Tuple[Set[str], Set[str], int]:
        """Get unique batteries, vehicles, and working-only vehicle count."""
        unique_batteries = set(battery_vehicles.keys())
        unique_vehicles = set(vehicle_info.keys())

        # Count working-only vehicles (those not in repair data)
        repair_vins = set()
        for events in battery_vehicles.values():
            for event in events:
                if event["column"] in ["old", "new"]:  # Repair events
                    repair_vins.add(event["vin"])

        working_only_count = len(unique_vehicles - repair_vins)

        return unique_batteries, unique_vehicles, working_only_count


class OutputHandler:
    """Handles validation, output generation, and logging results."""

    def __init__(self, today: datetime.date):
        self.today = today

    def validate_timelines(self, timelines: List[BatteryInterval]) -> bool:
        """
        Runs comprehensive validations on battery timelines.

        Args:
            timelines: List of BatteryInterval objects to validate

        Returns:
            bool: True if all validations pass, False otherwise
        """
        logger.info("Starting timeline validation...")

        validation_passed = True
        validation_passed &= self._validate_erstzulassung_correctness(timelines)
        validation_passed &= self._validate_battery_exclusivity(timelines)

        if validation_passed:
            logger.info("✅ All timeline validations passed")
        else:
            logger.warning("❌ Timeline validation failed - see details above")

        return validation_passed

    def _validate_erstzulassung_correctness(
        self, timelines: List[BatteryInterval]
    ) -> bool:
        """
        Validate that vehicles with erstzulassung_candidate intervals
        actually have their first timeline interval starting with the expected VIN.
        """
        logger.info("Validating erstzulassung correctness...")

        # Group timelines by VIN and sort by start date
        timelines_by_vin = defaultdict(list)
        for interval in timelines:
            timelines_by_vin[interval["vin"]].append(interval)

        # Sort intervals within each VIN by start date
        for vin in timelines_by_vin:
            timelines_by_vin[vin].sort(
                key=lambda x: x["interval_start"] or datetime.date.min
            )

        # Group intervals by battery for conflict checking
        battery_usage = defaultdict(list)
        for interval in timelines:
            battery_usage[interval["battery_id"]].append(interval)

        # Find all VINs that have erstzulassung_candidate intervals
        erstzulassung_candidate_vins = set()
        erstzulassung_intervals = []
        for interval in timelines:
            if interval.get("erstzulassung_candidate", False):
                erstzulassung_candidate_vins.add(interval["vin"])
                erstzulassung_intervals.append(interval)

        logger.info(
            f"Found {len(erstzulassung_candidate_vins)} VINs with erstzulassung_candidate intervals"
        )

        validation_errors = []

        # Validate each erstzulassung_candidate interval
        for interval in erstzulassung_intervals:
            vin = interval["vin"]
            battery_id = interval["battery_id"]
            start_date = interval["interval_start"]
            end_date = interval["interval_end"]

            # Check if this is the first interval for this VIN
            if vin not in timelines_by_vin or not timelines_by_vin[vin]:
                validation_errors.append(
                    f"VIN {vin} has erstzulassung_candidate but no timeline intervals"
                )
                continue

            first_interval = timelines_by_vin[vin][0]

            # Check if the first interval is indeed for this VIN
            if first_interval["vin"] != vin:
                validation_errors.append(
                    f"VIN {vin} erstzulassung validation failed: "
                    f"first interval belongs to VIN {first_interval['vin']}, not {vin}"
                )
                continue

            if start_date is None:
                validation_errors.append(
                    f"VIN {vin} erstzulassung validation failed: "
                    f"first interval has no start date despite erstzulassung_candidate processing"
                )
                continue

            # Check if battery is used elsewhere during erstzulassung period
            battery_conflicts = []
            for other_interval in battery_usage[battery_id]:
                if other_interval["vin"] == vin:
                    continue  # Skip same VIN

                other_start = other_interval["interval_start"]
                other_end = other_interval["interval_end"] or self.today
                period_end = end_date or datetime.date.max
                if other_start is None:
                    continue

                if start_date < other_end and other_start < period_end:
                    battery_conflicts.append(other_interval["vin"])

            if battery_conflicts:
                validation_errors.append(
                    f"VIN {vin} erstzulassung validation failed: "
                    f"battery {battery_id} conflicts with VINs {battery_conflicts} during erstzulassung period"
                )

        # Log validation results
        if validation_errors:
            logger.warning("Erstzulassung validation errors found:")
            for error in validation_errors[:10]:  # Show first 10
                logger.warning(f"  ✗ {error}")
            if len(validation_errors) > 10:
                logger.warning(f"  ... and {len(validation_errors) - 10} more errors")
        else:
            logger.info("✅ Erstzulassung validation passed")

        return len(validation_errors) == 0

    def _validate_battery_exclusivity(self, timelines: List[BatteryInterval]) -> bool:
        """
        Validate that each battery is only used in one vehicle at any given time.
        Check for overlapping intervals for the same battery across different VINs.
        """
        logger.info("Validating battery exclusivity...")

        # Group intervals by battery_id
        battery_usage = defaultdict(list)
        for interval in timelines:
            # Skip zero-duration intervals (start = end)
            start = interval["interval_start"]
            end = interval["interval_end"]
            if start is not None and end is not None and start == end:
                continue
            battery_usage[interval["battery_id"]].append(interval)

        exclusivity_violations = []
        total_batteries_checked = 0

        for battery_id, intervals in battery_usage.items():
            total_batteries_checked += 1
            if len(intervals) <= 1:
                continue  # Single interval, no conflicts possible

            # Check all pairs of intervals for this battery
            for i, interval1 in enumerate(intervals):
                for interval2 in intervals[i + 1 :]:
                    # Skip if same VIN (allowed)
                    if interval1["vin"] == interval2["vin"]:
                        continue

                    # Get interval bounds
                    start1 = interval1["interval_start"]
                    end1 = interval1["interval_end"] or self.today
                    start2 = interval2["interval_start"]
                    end2 = interval2["interval_end"] or self.today

                    # Skip if either interval has no start date
                    if start1 is None or start2 is None:
                        continue

                    # Check for overlap: intervals overlap if start1 < end2 and start2 < end1
                    if start1 < end2 and start2 < end1:
                        exclusivity_violations.append(
                            {
                                "battery_id": battery_id,
                                "vin1": interval1["vin"],
                                "vin1_start": start1,
                                "vin1_end": end1,
                                "vin2": interval2["vin"],
                                "vin2_start": start2,
                                "vin2_end": end2,
                            }
                        )

        logger.info(
            f"Checked {total_batteries_checked} batteries for exclusivity violations"
        )

        if exclusivity_violations:
            logger.warning("Battery exclusivity violations found:")
            for violation in exclusivity_violations[:10]:  # Show first 10
                logger.warning(
                    f"  ✗ Battery {violation['battery_id']}: "
                    f"VIN {violation['vin1']} ({violation['vin1_start']} to {violation['vin1_end']}) "
                    f"overlaps with VIN {violation['vin2']} ({violation['vin2_start']} to {violation['vin2_end']})"
                )
            if len(exclusivity_violations) > 10:
                logger.warning(
                    f"  ... and {len(exclusivity_violations) - 10} more violations"
                )
        else:
            logger.info("✅ Battery exclusivity validation passed")

        return len(exclusivity_violations) == 0

    def generate_outputs(
        self,
        timelines: List[BatteryInterval],
        unique_batteries: Set[str],
        unique_vehicles: Set[str],
        working_only_vehicles: int,
        errors: List[str],
    ) -> Tuple[str, str]:
        """
        Generate main output files (timeline CSV and statistics TXT).

        Args:
            timelines: List of BatteryInterval objects
            unique_batteries: Set of unique battery IDs
            unique_vehicles: Set of unique vehicle VINs
            working_only_vehicles: Count of working-only vehicles
            errors: List of error messages

        Returns:
            Tuple[str, str]: (timeline_csv_filename, stats_txt_filename)
        """
        logger.info("Generating output files...")

        # Generate enhanced timeline CSV output
        timeline_csv = None
        if timelines:
            timeline_df = pd.DataFrame(timelines)
            timeline_csv = "battery_lifecycle_timelines.csv"
            timeline_df.to_csv(timeline_csv, index=False)
            logger.info(f"Saved timeline data to {timeline_csv}")

        # Generate statistics file
        stats_filename = "battery_timeline_statistics.txt"
        with open(stats_filename, "w") as f:
            f.write("Battery Timeline Analysis Statistics\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Processing Date: {datetime.now()}\n")
            f.write(f"Total Batteries: {len(unique_batteries)}\n")
            f.write(f"Total Vehicles: {len(unique_vehicles)}\n")
            f.write(f"Working-only Vehicles: {working_only_vehicles}\n")
            f.write(f"Errors: {len(errors)}\n")

            # Enhanced statistics
            if timelines:
                f.write(f"Total Timeline Intervals: {len(timelines)}\n")
                active_intervals = len(
                    [i for i in timelines if i["interval_end"] is None]
                )
                f.write(f"Currently Active Intervals: {active_intervals}\n")
                completed_intervals = len(timelines) - active_intervals
                f.write(f"Completed Intervals: {completed_intervals}\n")

                # Confidence distribution
                confidences = [i["confidence"] for i in timelines]
                avg_confidence = (
                    sum(confidences) / len(confidences) if confidences else 0
                )
                f.write(f"Average Confidence: {avg_confidence:.3f}\n")

                # Interval type distribution
                interval_types = {}
                for interval in timelines:
                    interval_type = interval["interval_type"]
                    interval_types[interval_type] = (
                        interval_types.get(interval_type, 0) + 1
                    )

                f.write(f"\nInterval Type Distribution:\n")
                for interval_type, count in interval_types.items():
                    f.write(f"  {interval_type}: {count}\n")

            if errors:
                f.write("\nErrors:\n")
                for error in errors:
                    f.write(f"- {error}\n")

        logger.info(f"Saved statistics to {stats_filename}")

        return timeline_csv, stats_filename

    def generate_timeline_outputs(self, timelines: List[BatteryInterval]) -> str:
        """
        Generate timeline-specific CSV output file.

        Args:
            timelines: List of BatteryInterval objects

        Returns:
            str: Timeline CSV filename
        """
        logger.info("Generating timeline output files...")

        timeline_csv = None
        if timelines:
            timeline_df = pd.DataFrame(timelines)
            timeline_csv = "battery_lifecycle_timelines.csv"
            timeline_df.to_csv(timeline_csv, index=False)
            logger.info(f"Saved timeline data to {timeline_csv}")

        return timeline_csv

    def generate_dual_battery_report(
        self, timelines: List[BatteryInterval]
    ) -> Dict[str, Any]:
        """
        Generate report on dual-battery configurations.

        Args:
            timelines: List of BatteryInterval objects

        Returns:
            Dict containing dual battery analysis results
        """
        dual_battery_vins = []

        # Group intervals by VIN
        by_vin = defaultdict(list)
        for iv in timelines:
            by_vin[iv["vin"]].append(iv)

        for vin, intervals in by_vin.items():
            # Count currently active batteries (interval_end is None)
            concurrent = len([iv for iv in intervals if iv["interval_end"] is None])
            if concurrent == 2:
                dual_battery_vins.append(vin)

        return {
            "total_dual_battery_vehicles": len(dual_battery_vins),
            "dual_battery_vins": dual_battery_vins,
        }


class BatteryTimelineGenerator:
    """Calculate battery ages based on repair events and vehicle data."""

    def __init__(self):
        self.today = datetime.now().date()

        # Processing results - populated by VehicleDataManager
        self.daily_stats_by_vehicle = {}
        self.battery_vehicles = {}
        self.vehicle_info = {}
        self.vin_to_vehicle_id = {}
        self.unique_vehicles = set()
        self.unique_batteries = set()
        self.vin_without_vehicle_id = set()

        # Enhanced lifecycle tracking
        self.battery_processors = {}
        self.battery_timelines = []
        self.raw_battery_timelines = []

        # Conflict bookkeeping
        self.conflicts = []
        self.phantom_battery_timelines = []

        self.stats = {
            "total_batteries": 0,
            "total_vehicles": 0,
            "working_only_vehicles": 0,
            "errors": [],
        }

    def build_battery_timelines(self):
        logger.info("Building battery lifecycle timelines...")

        # Process each battery independently
        for battery_id in self.unique_batteries:
            if (
                battery_id not in self.battery_vehicles
                or not self.battery_vehicles[battery_id]
            ):
                logger.warning(f"Battery {battery_id} has no events")
                continue

            # Create processor for this battery
            processor = BatteryProcessor(battery_id)
            self.battery_processors[battery_id] = processor

            # Sort events chronologically with deterministic tie-breaking

            events = sorted(
                self.battery_vehicles[battery_id],
                key=lambda x: (
                    x["date"],
                    0 if x["column"] == "old" else 1,  # tie-breaker
                    x.get("event_id", 0),  # stable deterministic order
                ),
            )
            # Process events through state machine
            for event in events:
                processor.process_event(event)

            # Finalize and collect intervals
            intervals = processor.finalize()
            self.battery_timelines.extend(intervals)

        logger.info(
            f"Built timelines with {len(self.battery_timelines)} intervals for {len(self.battery_processors)} batteries"
        )

    def _vehicle_was_active(
        self, vin, start_date, end_date, daily_stats_by_vehicle, vin_to_vehicle_id
    ):
        """Check if vehicle drove during the specified date range."""
        vehicle_id = vin_to_vehicle_id.get(vin)
        if vehicle_id not in daily_stats_by_vehicle:
            return False

        stats = daily_stats_by_vehicle[vehicle_id]

        # Convert date objects to pandas Timestamps for comparison
        start_ts = pd.Timestamp(start_date) if start_date else None
        end_ts = pd.Timestamp(end_date) if end_date else None

        if start_ts is None or end_ts is None:
            return False

        window = stats[(stats["date"] >= start_ts) & (stats["date"] <= end_ts)]

        if window.empty:
            return False

        valid_records = window[
            (window["km_start"] >= 0)
            & (window["km_end"] >= 0)
            & (window["km_end"] >= window["km_start"])
        ]

        if valid_records.empty:
            return False

        km_driven = (
            valid_records["km_end"]
            .subtract(valid_records["km_start"], fill_value=0)
            .sum()
        )

        return km_driven > 1

    def preprocess_erstzulassung_candidates(
        self, timelines: List[BatteryInterval]
    ) -> List[BatteryInterval]:
        """
        Pre-process erstzulassung_candidate intervals (facts, not assumptions).

        These intervals represent first-appearance events that appeared as removals,
        indicating the battery must have been installed before that date.
        We set the start date to erstzulassung of the vehicle.

        Args:
            timelines: List of BatteryInterval dicts from BatteryProcessor

        Returns:
            List of processed BatteryInterval dicts with erstzulassung starts filled
        """
        logger.info("Pre-processing erstzulassung_candidate intervals...")

        # Group by VIN for processing
        by_vin = defaultdict(list)
        for iv in timelines:
            by_vin[iv["vin"]].append(iv)

        processed_timelines = []

        for vin, intervals in by_vin.items():
            for interval in intervals:
                if interval.get("erstzulassung_candidate", False):
                    vehicle_data = self.vehicle_info.get(vin, {})
                    erstzulassung = vehicle_data.get(
                        "erstzulassung"
                    ) or vehicle_data.get("first_active_date")

                    if erstzulassung and interval["interval_end"]:
                        # Set start date to erstzulassung, but handle edge case where
                        # erstzulassung is after removal (testing scenario)
                        interval["interval_start"] = (
                            erstzulassung
                            if erstzulassung < interval["interval_end"]
                            else interval["interval_end"]
                        )
                        interval["notes"] = (
                            (interval.get("notes") or "")
                            + " | start set from erstzulassung (high-confidence scenario)"
                        )
                        interval["confidence"] = 0.9
                        logger.info(
                            f"Set erstzulassung start for battery {interval['battery_id']} in VIN {vin}: {erstzulassung}"
                        )
                    else:
                        logger.warning(
                            f"Missing erstzulassung or interval_end for erstzulassung_candidate battery {interval['battery_id']} in VIN {vin}"
                        )
                        interval["interval_start"] = interval["interval_end"]
                        interval["confidence"] = 0.0
                        interval["notes"] = (
                            interval.get("notes") or ""
                        ) + " | missing erstzulassung or interval_end"

                processed_timelines.append(interval)

        return processed_timelines

    def stitch_vehicle_timelines(
        self,
        timelines: List[BatteryInterval],
        vehicle_info: Dict[str, Any],
        daily_stats_by_vehicle: Dict[int, pd.DataFrame],
        vin_to_vehicle_id,
        conflicts,
        max_gap_days=30,
    ) -> List[BatteryInterval]:
        """
        Activity-aware timeline stitching per VIN.

        Args:
            timelines: List of BatteryInterval dicts from BatteryProcessor
            vehicle_info: Dict[vin] -> {erstzulassung, first_active_date, ...}
            daily_stats_by_vehicle: Dict[vehicle_id] -> DataFrame with daily stats
            vin_to_vehicle_id: Dict[vin] -> vehicle_id mapping
            max_gap_days: Max days to auto-fill without activity check

        Returns:
            List of stitched BatteryInterval dicts
        """

        usage_by_batt: Dict[str, List[BatteryInterval]] = defaultdict(list)
        by_vin = defaultdict(list)
        for iv in timelines:
            usage_by_batt[iv["battery_id"]].append(iv)
            by_vin[iv["vin"]].append(iv)

        def _battery_used_elsewhere(
            batt_id,
            a,
            b,
            this_vin,
        ):
            """
            Check if battery is used elsewhere during the given time period.

            Returns:
                tuple: (has_conflict: bool, conflict_object: dict or None)
                - has_conflict: True if battery is used elsewhere during [a, b)
                - conflict_object: dict with conflict details if has_conflict is True, None otherwise
                  Contains: vin, start_date, end_date
            """
            for iv in usage_by_batt.get(batt_id, []):
                if iv["vin"] == this_vin:
                    continue
                s = iv["interval_start"] or date.min
                e = iv["interval_end"] or self.today
                if s < b and a < e:
                    # Found a conflict - return immediately with conflict details
                    conflict_object = {
                        "vin": iv["vin"],
                        "start_date": iv["interval_start"],
                        "end_date": iv["interval_end"],
                        "source_event_ids": iv["source_event_ids"],
                    }
                    return True, conflict_object

            # No conflicts found
            return False, None

        stitched = []
        for vin, intervals in by_vin.items():
            if not intervals:
                continue

            # 1. Sort chronologically
            intervals.sort(
                key=lambda iv: (
                    iv["interval_start"] or iv["interval_end"] or date(1900, 1, 1),
                    iv["interval_end"] or self.today,
                )
            )

            # # ----------------
            # # 2. Middle gaps and orphaned removal fixes
            # # ----------------
            # filled = []
            # if len(intervals) >= 2:
            #     for prev, nxt in zip(intervals, intervals[1:]):
            #         filled.append(prev)

            #         # if nxt == intervals[-1]:
            #         #     continue

            #         if (
            #             nxt["interval_start"] is None
            #             and prev["interval_end"] is not None
            #         ):

            #             # Calculate gap duration, if there is no end date, assume 1 year
            #             gap_end_date = nxt["interval_end"] or prev[
            #                 "interval_end"
            #             ] + timedelta(days=365)
            #             gap_days = (gap_end_date - prev["interval_end"]).days

            #             # Check activity during gap
            #             activity_window_end = min(
            #                 gap_end_date, prev["interval_end"] + timedelta(days=365)
            #             )
            #             was_active = self._vehicle_was_active(
            #                 vin,
            #                 prev["interval_end"],
            #                 activity_window_end,
            #                 daily_stats_by_vehicle,
            #                 vin_to_vehicle_id,
            #             )
            #             if was_active or gap_days <= max_gap_days:
            #                 # Vehicle was active, that means a battery must have been installed
            #                 has_conflict, conflict_obj = _battery_used_elsewhere(
            #                     nxt["battery_id"],
            #                     prev["interval_end"],
            #                     gap_end_date,
            #                     vin,
            #                 )
            #                 if has_conflict:
            #                     # Add conflict to the conflicts list
            #                     conflict = {
            #                         "type": "gap_filling",
            #                         "battery_id": nxt["battery_id"],
            #                         "candidate_vin": vin,
            #                         "other_vin": conflict_obj["vin"],
            #                         "candidate_vin_start_day": prev["interval_end"],
            #                         "candidate_vin_end_day": gap_end_date,
            #                         "other_vin_start_date": conflict_obj["start_date"],
            #                         "other_vin_end_date": conflict_obj["end_date"],
            #                         "confidence": nxt.get("confidence", 1.0),
            #                         "candidate_source_event_ids": nxt[
            #                             "source_event_ids"
            #                         ],
            #                     }
            #                     conflicts.append(conflict)
            #                     logger.info(conflict)

            #                     nxt["interval_start"] = prev["interval_end"]
            #                     nxt["notes"] = (
            #                         (nxt.get("notes") or "")
            #                         + " | start imputed from previous removal, but battery used elsewhere"
            #                     )
            #                     nxt["interval_type"] = "infer_start_record_end"
            #                     nxt["confidence"] = min(nxt.get("confidence", 1.0), 0.5)
            #                 else:
            #                     nxt["interval_start"] = prev["interval_end"]
            #                     nxt["notes"] = (
            #                         nxt.get("notes") or ""
            #                     ) + " | start imputed from previous removal"
            #                     nxt["interval_type"] = "infer_start_record_end"
            #                     nxt["confidence"] = min(nxt.get("confidence", 1.0), 0.6)
            #             else:
            #                 # Vehicle was inactive
            #                 nxt["interval_start"] = nxt["interval_end"]
            #                 nxt["notes"] = (
            #                     (nxt.get("notes") or "")
            #                     + " | zero duration - vehicle was inactive during this period"
            #                 )
            #                 nxt["confidence"] = min(nxt.get("confidence", 1.0), 0.1)
            #                 nxt["interval_type"] = "infer_start_record_end"

            #         elif nxt["interval_start"] is None and prev["interval_end"] is None:
            #             # Dual-battery setup: both batteries active simultaneously
            #             # Keep them as concurrent intervals rather than overlapping
            #             nxt["interval_start"] = prev["interval_start"]
            #             nxt["notes"] = "Concurrent battery in dual-battery vehicle"
            #             nxt["interval_type"] = "infer_start_record_end"
            #             nxt["confidence"] = 0.6
            #             prev["confidence"] = 0.6

            #         # Handle small gaps between known intervals
            #         elif (
            #             prev["interval_end"]
            #             and nxt["interval_start"]
            #             and (nxt["interval_start"] - prev["interval_end"]).days > 1
            #         ):
            #             # Probably maintaince event
            #             pass

            #     # Add final interval
            #     filled.append(intervals[-1])
            # else:
            #     filled = intervals.copy()

            # intervals = filled

            # # ----------------
            # # 3. Erstzulassung extension (vehicle can not drive without a battery)
            # # Note: erstzulassung_candidate intervals are already handled in preprocessing step
            # # ----------------
            # if intervals and intervals[0]["interval_start"] is None:
            #     vehicle_data = vehicle_info.get(vin, {})
            #     start_date = vehicle_data.get("erstzulassung")

            #     if (
            #         start_date and intervals[0]["interval_end"]
            #     ):  # Only if we have a real end date
            #         has_conflict, conflict_obj = _battery_used_elsewhere(
            #             intervals[0]["battery_id"],
            #             start_date,
            #             intervals[0]["interval_end"],
            #             vin,
            #         )
            #         if not has_conflict:
            #             intervals[0]["interval_start"] = start_date
            #             intervals[0]["notes"] = (
            #                 intervals[0].get("notes") or ""
            #             ) + " | start imputed from erstzulassung"
            #             intervals[0]["confidence"] = 0.75
            #             intervals[0]["interval_type"] = "infer_start_record_end"
            #         else:
            #             # Add conflict to the conflicts list
            #             conflict = {
            #                 "type": "erstzulassung_extension",
            #                 "battery_id": intervals[0]["battery_id"],
            #                 "candidate_vin": vin,
            #                 "other_vin": conflict_obj["vin"],
            #                 "candidate_vin_start_day": start_date,
            #                 "candidate_vin_end_day": intervals[0]["interval_end"],
            #                 "other_vin_start_date": conflict_obj["start_date"],
            #                 "other_vin_end_date": conflict_obj["end_date"],
            #                 "confidence": intervals[0].get("confidence", 1.0),
            #                 "candidate_source_event_ids": intervals[0][
            #                     "source_event_ids"
            #                 ],
            #             }
            #             conflicts.append(conflict)
            #             logger.info(conflict)
            #     else:
            #         logger.warning(f"Vehicle {vin} has no erstzulassung date")
            #         intervals[0]["interval_start"] = intervals[0]["interval_end"]
            #         intervals[0]["confidence"] = 0
            #         intervals[0]["notes"] = (
            #             intervals[0].get("notes") or ""
            #         ) + " | no erstzulassung or first active date"
            #         intervals[0]["interval_type"] = "infer_start_record_end"

            # elif intervals and intervals[0]["interval_start"] is not None:
            #     # Check if first interval starts after vehicle's erstzulassung
            #     vehicle_data = vehicle_info.get(vin, {})
            #     erstzulassung = vehicle_data.get("erstzulassung")

            #     if erstzulassung and intervals[0]["interval_start"] > erstzulassung:
            #         # Gap between erstzulassung and first recorded battery
            #         has_conflict, conflict_obj = _battery_used_elsewhere(
            #             intervals[0]["battery_id"],
            #             erstzulassung,
            #             intervals[0]["interval_start"],
            #             vin,
            #         )
            #         if not has_conflict:
            #             interval = {
            #                 "battery_id": intervals[0]["battery_id"],
            #                 "vin": vin,
            #                 "interval_start": erstzulassung,
            #                 "interval_end": intervals[0]["interval_start"],
            #                 "interval_type": "infer_start_infer_end",
            #                 "source_event_ids": intervals[0]["source_event_ids"],
            #                 "confidence": 0.7,
            #                 "notes": "Imputed interval - vehicle active before first recorded battery battery not used elsewhere",
            #             }
            #             intervals.insert(0, interval)
            #         else:
            #             # Add conflict to the conflicts list
            #             conflict = {
            #                 "type": "erstzulassung_extension",
            #                 "battery_id": intervals[0]["battery_id"],
            #                 "candidate_vin": vin,
            #                 "other_vin": conflict_obj["vin"],
            #                 "candidate_vin_start_day": erstzulassung,
            #                 "candidate_vin_end_day": intervals[0]["interval_start"],
            #                 "other_vin_start_date": conflict_obj["start_date"],
            #                 "other_vin_end_date": conflict_obj["end_date"],
            #                 "confidence": intervals[0].get("confidence", 1.0),
            #                 "candidate_source_event_ids": intervals[0][
            #                     "source_event_ids"
            #                 ],
            #             }
            #             conflicts.append(conflict)
            #             logger.info(conflict)

            #             # Insert a battery with no known start (this will be resolved in next stage)
            #             interval = {
            #                 "battery_id": intervals[0]["battery_id"],
            #                 "vin": vin,
            #                 "interval_start": None,
            #                 "interval_end": intervals[0]["interval_start"],
            #                 "interval_type": "infer_start_infer_end",
            #                 "source_event_ids": intervals[0]["source_event_ids"],
            #                 "confidence": 0.4,
            #                 "notes": "Imputed interval - vehicle active before first recorded battery, but recorded battery used elsewhere",
            #             }
            #             intervals.insert(0, interval)

            # ----------------
            # 3. Post-activity extension (Last interval is CLOSED, but daily-stats show km afterwards.)
            # ----------------
            last_iv = intervals[-1] if intervals else None

            if last_iv and last_iv["interval_end"] is not None:
                was_active_after = self._vehicle_was_active(
                    vin,
                    last_iv["interval_end"] + timedelta(days=90),
                    self.today,
                    daily_stats_by_vehicle,
                    vin_to_vehicle_id,
                )

                if was_active_after:
                    has_conflict, conflict_obj = _battery_used_elsewhere(
                        last_iv["battery_id"],
                        last_iv["interval_end"],
                        self.today,
                        vin,
                    )
                    if not has_conflict:
                        new_iv = {
                            "battery_id": last_iv["battery_id"],
                            "vin": vin,
                            "interval_start": last_iv["interval_end"],
                            "interval_end": self.today,
                            "interval_type": "infer_start_infer_end",
                            "battery_status": "active",
                            "source_event_ids": last_iv["source_event_ids"],
                            "confidence": 0.75,
                            "notes": (
                                "Imputed interval - vehicle active after last removal, battery assumed to be still inside"
                            ),
                        }
                        intervals.append(new_iv)
                        usage_by_batt[last_iv["battery_id"]].append(new_iv)
                    else:
                        if conflict_obj["end_date"] < self.today:
                            new_iv = {
                                "battery_id": last_iv["battery_id"],
                                "vin": vin,
                                "interval_start": conflict_obj["end_date"],
                                "interval_end": self.today,
                                "interval_type": "infer_start_infer_end",
                                "source_event_ids": last_iv["source_event_ids"],
                                "confidence": 0.6,
                                "notes": (
                                    "Imputed interval - vehicle active after last removal, battery assumed to be still inside, set start to conflicting interval end date"
                                ),
                            }
                            intervals.append(new_iv)
                            usage_by_batt[last_iv["battery_id"]].append(new_iv)
                        else:
                            conflict = {
                                "type": "post_activity_extension",
                                "battery_id": last_iv["battery_id"],
                                "candidate_vin": vin,
                                "other_vin": conflict_obj["vin"],
                                "candidate_vin_start_day": last_iv["interval_end"],
                                "candidate_vin_end_day": self.today,
                                "other_vin_start_date": conflict_obj["start_date"],
                                "other_vin_end_date": conflict_obj["end_date"],
                                "confidence": 0.3,
                                "candidate_source_event_ids": last_iv[
                                    "source_event_ids"
                                ],
                                "other_vin_source_event_ids": conflict_obj[
                                    "source_event_ids"
                                ],
                            }
                            conflicts.append(conflict)
                            logger.info(conflict)

            stitched.extend(intervals)
        return stitched, conflicts

    def resolve_battery_conflicts(
        self, stitched_timelines: List[BatteryInterval], conflicts, vehicle_info
    ):
        if not conflicts:
            logger.info("No conflicts to resolve")
            return stitched_timelines

        # Build sacred intervals index - these are NEVER modified during conflict resolution
        sacred_battery_erstzulassung_intervals = defaultdict()
        for iv in stitched_timelines:
            if iv.get("erstzulassung_candidate", False):
                sacred_battery_erstzulassung_intervals[iv["battery_id"]] = {
                    "vin": iv["vin"],
                    "interval_start": iv["interval_start"],
                    "interval_end": iv["interval_end"],
                }

        # Build O(1) lookup index: (battery_id, vin) -> list of intervals
        timeline_index = defaultdict(list)
        for iv in stitched_timelines:
            key = (iv["battery_id"], iv["vin"])
            timeline_index[key].append(iv)

        # Group conflicts by battery and type
        erstzulassung_conflicts_by_batt = defaultdict(list)
        post_activity_conflicts_by_batt = defaultdict(list)
        for conflict in conflicts:
            if conflict["type"] == "erstzulassung_extension":
                bat = conflict["battery_id"]
                erstzulassung_conflicts_by_batt[bat].append(conflict)
            elif conflict["type"] == "post_activity_extension":
                bat = conflict["battery_id"]
                post_activity_conflicts_by_batt[bat].append(conflict)

        # Resolve erstzulassung conflicts while respecting sacred intervals
        for bat, conflicts in erstzulassung_conflicts_by_batt.items():
            logger.info(f"Resolving erstzulassung conflicts for battery {bat}")

            # Sort conflicts by earliest start date and source event IDs
            conflicts.sort(
                key=lambda x: (
                    x["candidate_vin_start_day"],
                    (
                        min(x["candidate_source_event_ids"])
                        if x["candidate_source_event_ids"]
                        else float("inf")
                    ),
                    x["candidate_vin_end_day"],
                )
            )

            # Use sweep line algorithm to sequence intervals
            intervals_to_resolve = []
            for conflict in conflicts:
                lookup_key = (bat, conflict["candidate_vin"])
                if lookup_key in timeline_index:
                    for iv in timeline_index[lookup_key]:
                        if (
                            iv["interval_start"] is None
                            and iv["interval_end"] == conflict["candidate_vin_end_day"]
                        ):
                            intervals_to_resolve.append(
                                {
                                    "interval": iv,
                                    "conflict": conflict,
                                    "desired_start": conflict[
                                        "candidate_vin_start_day"
                                    ],
                                }
                            )
                            break

            if not intervals_to_resolve:
                continue

            # Sort intervals by desired start date
            intervals_to_resolve.sort(key=lambda x: x["desired_start"])

            # Sequential assignment with sweep line logic

            if bat in sacred_battery_erstzulassung_intervals.keys():
                current_start_date = sacred_battery_erstzulassung_intervals[bat][
                    "interval_end"
                ]
            else:
                current_start_date = intervals_to_resolve[0]["desired_start"]

            for i, item in enumerate(intervals_to_resolve):
                iv = item["interval"]
                conflict = item["conflict"]

                # Ensure start <= end
                if current_start_date <= iv["interval_end"]:
                    iv["interval_start"] = current_start_date
                    iv["notes"] = (
                        (iv["notes"] or "")
                        + f" | start imputed from sequential conflict resolution with {conflict['other_vin']} (position {i})"
                    )
                    iv["confidence"] = max(
                        0.6 - (i * 0.1), 0.3
                    )  # Decrease confidence for later assignments
                    iv["interval_type"] = "infer_start_record_end"

                    logger.info(
                        f"  → RESOLVED: Set {conflict['candidate_vin']} start to {current_start_date} (position {i})"
                    )

                    # Next interval starts when this one ends
                    current_start_date = iv["interval_end"]
                else:
                    # start > end -> set start to end (zero duration)
                    iv["interval_start"] = iv["interval_end"]
                    iv["notes"] = (
                        (iv["notes"] or "")
                        + f" | start set to end due to conflict resolution with {conflict['other_vin']} (position {i})"
                    )
                    iv["confidence"] = 0.2
                    iv["interval_type"] = "infer_start_record_end"

                    logger.info(
                        f"  → RESOLVED (zero duration): Set {conflict['candidate_vin']} start=end={iv['interval_end']} (position {i})"
                    )

        for bat, conflicts in post_activity_conflicts_by_batt.items():
            logger.info(f"Resolving post-activity conflicts for battery {bat}")
        return stitched_timelines

    def limit_concurrent_batteries_per_vin(
        self, stitched_timelines: List[BatteryInterval]
    ) -> List[BatteryInterval]:
        """
        Ensure each VIN has at most 2 active batteries at any point in time.
        Uses sweep line algorithm to detect and resolve historical overlaps.
        """
        logger.info(
            "Limiting concurrent batteries per VIN to maximum 2 (historical + current)..."
        )

        # Group by VIN
        by_vin = defaultdict(list)
        for iv in stitched_timelines:
            by_vin[iv["vin"]].append(iv)

        for vin, intervals in by_vin.items():
            if len(intervals) <= 2:
                continue  # No possible overlaps

            # Build events list: (date, event_type, interval_obj)
            events = []
            for iv in intervals:
                start_date = iv["interval_start"] or date.min
                end_date = iv["interval_end"] or self.today

                events.append((start_date, "start", iv))
                if (
                    iv["interval_end"] is not None
                ):  # Don't add end event for open intervals
                    events.append((end_date, "end", iv))

            # Sort by date, with "end" events before "start" events on same date
            events.sort(key=lambda x: (x[0], x[1] == "start"))

            # Sweep line algorithm
            active_intervals = []

            for event_date, event_type, interval_obj in events:
                if event_type == "start":
                    # Validate interval has positive duration before considering it active
                    start_date = interval_obj["interval_start"]
                    end_date = interval_obj["interval_end"]

                    # Only add to active if it has positive duration
                    if start_date and end_date and start_date < end_date:
                        active_intervals.append(interval_obj)
                    elif start_date and not end_date:
                        # Open interval (currently active) - also valid
                        active_intervals.append(interval_obj)
                    else:
                        # Zero duration or invalid interval - skip
                        logger.debug(
                            f"Skipping invalid interval for battery {interval_obj['battery_id']}: start={start_date}, end={end_date}"
                        )
                        continue

                    # Check if we exceed 2 concurrent batteries
                    if len(active_intervals) > 2:
                        logger.warning(
                            f"VIN {vin} has {len(active_intervals)} concurrent batteries on {event_date}"
                        )

                        # Sort by confidence (lowest first) and close the worst one
                        active_intervals.sort(
                            key=lambda x: (
                                x.get("confidence", 0),
                                max(
                                    x.get("source_event_ids", [0]) or [0]
                                ),  # Lower max IDs come first
                            )
                        )

                        # Close the lowest confidence interval
                        interval_to_close = active_intervals.pop(0)
                        interval_to_close["interval_end"] = event_date
                        interval_to_close["notes"] = (
                            interval_to_close.get("notes", "")
                            + f" | Closed on {event_date} due to >2 concurrent batteries limit"
                        )
                        # interval_to_close["confidence"] = min(
                        #     interval_to_close.get("confidence", 1.0), 0.3
                        # )

                        logger.info(
                            f"  → Closed battery {interval_to_close['battery_id']} on {event_date}"
                        )
                        logger.info(" ")

                elif event_type == "end":
                    # Remove from active set
                    active_intervals = [
                        iv for iv in active_intervals if iv != interval_obj
                    ]

        return stitched_timelines

    def generate_outputs(self):
        """Generate output files."""
        logger.info("Generating output files...")

        # Generate enhanced timeline CSV output
        if hasattr(self, "battery_timelines") and self.battery_timelines:
            timeline_df = pd.DataFrame(self.battery_timelines)
            timeline_csv = f"battery_lifecycle_timelines.csv"
            timeline_df.to_csv(timeline_csv, index=False)
            logger.info(f"Saved timeline data to {timeline_csv}")

        # Generate statistics file
        stats_filename = f"battery_timeline_statistics.txt"
        with open(stats_filename, "w") as f:
            f.write("Battery Timeline Analysis Statistics\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Processing Date: {datetime.now()}\n")
            f.write(f"Total Batteries: {len(self.unique_batteries)}\n")
            f.write(f"Total Vehicles: {len(self.unique_vehicles)}\n")
            f.write(f"Working-only Vehicles: {self.stats['working_only_vehicles']}\n")
            f.write(f"Errors: {len(self.stats['errors'])}\n")

            # Enhanced statistics
            if hasattr(self, "battery_timelines"):
                f.write(f"Total Timeline Intervals: {len(self.battery_timelines)}\n")
                active_intervals = len(
                    [i for i in self.battery_timelines if i["interval_end"] is None]
                )
                f.write(f"Currently Active Intervals: {active_intervals}\n")
                completed_intervals = len(self.battery_timelines) - active_intervals
                f.write(f"Completed Intervals: {completed_intervals}\n")

            if self.stats["errors"]:
                f.write("\nErrors:\n")
                for error in self.stats["errors"]:
                    f.write(f"- {error}\n")

        logger.info(f"Saved statistics to {stats_filename}")

        # Return the timeline CSV if it exists, otherwise return None
        if hasattr(self, "battery_timelines") and self.battery_timelines:
            return timeline_csv, stats_filename
        else:
            return None, stats_filename

    def generate_timeline_outputs(self):
        """Generate timeline output files only."""
        logger.info("Generating timeline output files...")

        # Generate timeline CSV output
        if hasattr(self, "battery_timelines") and self.battery_timelines:
            timeline_df = pd.DataFrame(self.battery_timelines)
            timeline_csv = f"battery_lifecycle_timelines.csv"
            timeline_df.to_csv(timeline_csv, index=False)
            logger.info(f"Saved timeline data to {timeline_csv}")

            # Generate timeline statistics
            stats_filename = f"battery_timeline_statistics.txt"
            with open(stats_filename, "w") as f:
                f.write("Battery Timeline Analysis Statistics\n")
                f.write("=" * 40 + "\n\n")
                f.write(f"Processing Date: {datetime.now()}\n")
                f.write(f"Total Batteries: {len(self.battery_processors)}\n")
                f.write(f"Total Timeline Intervals: {len(self.battery_timelines)}\n")

                active_intervals = len(
                    [i for i in self.battery_timelines if i["interval_end"] is None]
                )
                f.write(f"Currently Active Intervals: {active_intervals}\n")
                completed_intervals = len(self.battery_timelines) - active_intervals
                f.write(f"Completed Intervals: {completed_intervals}\n")

                # Confidence distribution
                confidences = [i["confidence"] for i in self.battery_timelines]
                avg_confidence = (
                    sum(confidences) / len(confidences) if confidences else 0
                )
                f.write(f"Average Confidence: {avg_confidence:.3f}\n")

                # Interval type distribution
                interval_types = {}
                for interval in self.battery_timelines:
                    interval_type = interval["interval_type"]
                    interval_types[interval_type] = (
                        interval_types.get(interval_type, 0) + 1
                    )

                f.write(f"\nInterval Type Distribution:\n")
                for interval_type, count in interval_types.items():
                    f.write(f"  {interval_type}: {count}\n")

            logger.info(f"Saved timeline statistics to {stats_filename}")
            return timeline_csv
        else:
            logger.warning("No timeline data to output")
            return None

    def validate_timelines(self, battery_timelines):
        logger.info("Validating timelines...")

        def _validate_erstzulassung_correctness():
            """
            Validate that vehicles with erstzulassung_candidate intervals
            actually have their first timeline interval starting with the expected VIN.
            """
            logger.info("Validating erstzulassung correctness...")

            # Step 1: Find all VINs that have erstzulassung_candidate intervals
            erstzulassung_candidate_vins = set()
            erstzulassung_intervals = []
            for interval in battery_timelines:
                if interval.get("erstzulassung_candidate", False):
                    erstzulassung_candidate_vins.add(interval["vin"])
                    erstzulassung_intervals.append(interval)

            logger.info(
                f"Found {len(erstzulassung_candidate_vins)} VINs with erstzulassung_candidate intervals"
            )

            # Step 2: Group timelines by VIN and sort chronologically
            timelines_by_vin = defaultdict(list)
            for interval in battery_timelines:
                timelines_by_vin[interval["vin"]].append(interval)

            # Sort each VIN's intervals chronologically
            for vin in timelines_by_vin:
                timelines_by_vin[vin].sort(
                    key=lambda iv: (
                        iv["interval_start"] or iv["interval_end"] or date(1900, 1, 1),
                        iv["interval_end"] or self.today,
                    )
                )

            # Step 3: Build battery usage index for conflict detection
            battery_usage = defaultdict(list)
            for interval in battery_timelines:
                battery_usage[interval["battery_id"]].append(interval)

            # Step 4: Validate each erstzulassung_candidate interval
            validation_errors = []
            validation_successes = []

            for interval in erstzulassung_intervals:
                vin = interval["vin"]
                battery_id = interval["battery_id"]
                start_date = interval["interval_start"]
                end_date = interval["interval_end"]

                # Check if this is the first interval for this VIN
                if vin not in timelines_by_vin or not timelines_by_vin[vin]:
                    validation_errors.append(
                        f"VIN {vin} has erstzulassung_candidate but no timeline intervals"
                    )
                    continue

                first_interval = timelines_by_vin[vin][0]

                # Check if the first interval is indeed for this VIN
                if first_interval["vin"] != vin:
                    validation_errors.append(
                        f"VIN {vin} erstzulassung validation failed: "
                        f"first interval belongs to VIN {first_interval['vin']}, not {vin}"
                    )
                    continue

                if start_date is None:
                    validation_errors.append(
                        f"VIN {vin} erstzulassung validation failed: "
                        f"first interval has no start date despite erstzulassung_candidate processing"
                    )
                    continue

                # NEW: Check if battery is used elsewhere during erstzulassung period
                battery_conflicts = []
                for other_interval in battery_usage[battery_id]:
                    if other_interval["vin"] == vin:
                        continue  # Skip same VIN

                    other_start = other_interval["interval_start"]
                    other_end = other_interval["interval_end"] or self.today
                    period_end = end_date or date.max
                    if other_start is None:
                        continue

                    if start_date < other_end and other_start < period_end:
                        battery_conflicts.append(other_interval["vin"])

                if battery_conflicts:
                    validation_errors.append(
                        f"VIN {vin} erstzulassung validation failed: "
                        f"battery {battery_id} also used in VINs {battery_conflicts} during erstzulassung period"
                    )
                else:
                    validation_successes.append(
                        f"VIN {vin} erstzulassung validation passed: "
                        f"battery {battery_id} exclusively used, starts on {start_date}"
                    )

            # Step 5: Report validation results
            logger.info(f"Erstzulassung validation completed:")
            logger.info(f"  - {len(validation_successes)} VINs passed validation")
            logger.info(f"  - {len(validation_errors)} VINs failed validation")

            if validation_errors:
                logger.warning("Validation errors found:")
                for error in validation_errors:
                    logger.warning(f"  ✗ {error}")

            return len(validation_errors) == 0

        def _validate_battery_exclusivity():
            """
            Validate that each battery is only used in one vehicle at any given time.
            Check for overlapping intervals for the same battery across different VINs.
            """
            logger.info("Validating battery exclusivity...")

            # Group intervals by battery_id
            battery_usage = defaultdict(list)
            for interval in battery_timelines:
                # Skip zero-duration intervals (start = end)
                start = interval["interval_start"]
                end = interval["interval_end"]
                if start is not None and end is not None and start == end:
                    continue
                battery_usage[interval["battery_id"]].append(interval)

            exclusivity_violations = []
            total_batteries_checked = 0

            for battery_id, intervals in battery_usage.items():
                total_batteries_checked += 1

                # Skip if battery only used in one interval
                if len(intervals) <= 1:
                    continue

                # Check all pairs of intervals for this battery
                for i, interval1 in enumerate(intervals):
                    for j, interval2 in enumerate(intervals[i + 1 :], i + 1):
                        # Skip if same VIN (allowed for sequential usage)
                        if interval1["vin"] == interval2["vin"]:
                            continue

                        # Get interval bounds
                        start1 = interval1["interval_start"]
                        end1 = interval1["interval_end"]
                        start2 = interval2["interval_start"]
                        end2 = interval2["interval_end"]

                        if not start1 or not end1 or not start2 or not end2:
                            continue

                        # Check for overlap: start1 < end2 AND start2 < end
                        if start1 < end2 and start2 < end1:
                            violation = {
                                "battery_id": battery_id,
                                "vin1": interval1["vin"],
                                "vin1_start": interval1["interval_start"],
                                "vin1_end": interval1["interval_end"],
                                "vin2": interval2["vin"],
                                "vin2_start": interval2["interval_start"],
                                "vin2_end": interval2["interval_end"],
                                "overlap_start": max(start1, start2),
                                "overlap_end": min(end1, end2),
                            }
                            exclusivity_violations.append(violation)

            # Report results
            logger.info(f"Battery exclusivity validation completed:")
            logger.info(f"  - {total_batteries_checked} batteries checked")
            logger.info(
                f"  - {len(exclusivity_violations)} exclusivity violations found"
            )

            if exclusivity_violations:
                logger.warning("Battery exclusivity violations found:")
                for violation in exclusivity_violations[:10]:  # Show first 10
                    logger.warning(
                        f"  ✗ Battery {violation['battery_id']}: "
                        f"VIN {violation['vin1']} ({violation['vin1_start']} to {violation['vin1_end']}) "
                        f"overlaps with VIN {violation['vin2']} ({violation['vin2_start']} to {violation['vin2_end']})"
                    )
                if len(exclusivity_violations) > 10:
                    logger.warning(
                        f"  ... and {len(exclusivity_violations) - 10} more violations"
                    )

            return len(exclusivity_violations) == 0

        _validate_erstzulassung_correctness()
        _validate_battery_exclusivity()

    def resolve_battery_timelines(self, battery_timelines):
        by_battery = defaultdict(list)
        for iv in battery_timelines:
            by_battery[iv["battery_id"]].append(iv)
        new_timelines = []
        for ivs in by_battery.values():
            resolved = self._resolve_overlaps_for_battery(ivs)
            new_timelines.extend(resolved)
        return new_timelines

    def _resolve_overlaps_for_battery(
        self, intervals: List[BatteryInterval]
    ) -> List[BatteryInterval]:
        if len(intervals) <= 1:
            return intervals

        events = []
        for iv in intervals:
            start = iv["interval_start"]
            end = iv["interval_end"]
            if start is None or end is None or start >= end:
                iv["interval_start"] = iv["interval_end"]  # Make empty
                iv["notes"] += " | Invalid interval, made empty"
                continue
            events.append((start, 1, iv))  # start
            events.append((end, -1, iv))  # end

        events.sort(
            key=lambda e: (e[0], e[1])
        )  # Sort by time, ends before starts if tie

        active = []
        last_time = None
        assigned_segments = defaultdict(list)  # vin -> list of (start, end) segments

        for time, typ, iv in events:
            if last_time is not None and time > last_time:
                if active:
                    # Assign segment to highest-confidence active interval
                    max_iv = max(active, key=lambda x: x["confidence"])
                    assigned_segments[max_iv["vin"]].append((last_time, time))
            if typ == 1:  # start
                active.append(iv)
            else:  # end
                if iv in active:
                    active.remove(iv)
            last_time = time

        # Process assigned segments for each original interval
        new_intervals = []
        for iv in intervals:
            segs = assigned_segments.get(iv["vin"], [])
            if not segs:
                iv["interval_start"] = iv["interval_end"]
                iv["notes"] += " | Trimmed to empty in overlap resolution"
                new_intervals.append(iv)
                continue

            # Merge adjacent segments
            segs.sort(key=lambda s: s[0])
            merged = []
            for s in segs:
                if not merged or merged[-1][1] < s[0]:
                    merged.append(list(s))
                else:
                    merged[-1][1] = max(merged[-1][1], s[1])

            # Handle merged segments (split if disjoint)
            for j, m in enumerate(merged):
                if j > 0:
                    # Create new interval for split
                    new_iv = iv.copy()
                    new_iv["interval_start"] = m[0]
                    new_iv["interval_end"] = m[1]
                    new_iv[
                        "notes"
                    ] += f" | Split in overlap resolution ({j+1}/{len(merged)})"
                    new_intervals.append(new_iv)
                else:
                    # Update original
                    iv["interval_start"] = m[0]
                    iv["interval_end"] = m[1]
                    iv["notes"] += " | Trimmed in overlap resolution"
                    new_intervals.append(iv)

        return new_intervals

    def run(self):
        """Run the battery timeline analysis process."""
        logger.info("Starting battery timeline analysis...")

        try:
            # Phase 1: Data Loading using DataLoader
            data_loader = DataLoader()
            engine = data_loader.initialize_database_connection()
            vin_to_vehicle_id = data_loader.load_vin_mappings(engine)
            hv_repair_df, working_vehicles_df, working_unique_df, daily_stats_df = (
                data_loader.load_data()
            )

            # Combine working vehicles for cleaning
            working_vehicles_combined = pd.concat(
                [working_vehicles_df, working_unique_df], ignore_index=True
            )
            hv_repair_df, working_vehicles_df = data_loader.clean_data(
                hv_repair_df, working_vehicles_combined
            )
            daily_stats_by_vehicle = data_loader.pre_index_daily_stats(daily_stats_df)

            # Collect any loading errors
            self.stats["errors"].extend(data_loader.errors)

            # Clear daily_stats_df to free memory
            daily_stats_df = None

            logger.info("Data loading phase completed successfully")

            # Phase 2: Data Aggregation using VehicleDataManager
            vehicle_manager = VehicleDataManager(
                daily_stats_by_vehicle, vin_to_vehicle_id
            )
            self.vehicle_info = vehicle_manager.build_vehicle_info(
                working_vehicles_df, hv_repair_df
            )
            self.battery_vehicles, self.unique_batteries, self.unique_vehicles = (
                vehicle_manager.process_hv_repair_data(hv_repair_df)
            )

            repair_vins = set(hv_repair_df["vin"].unique())
            self.battery_vehicles, self.unique_batteries, working_only_count = (
                vehicle_manager.add_vehicles_from_working_only_data(
                    self.battery_vehicles, self.vehicle_info, repair_vins
                )
            )

            # Update stats
            self.stats["total_batteries"] = len(self.unique_batteries)
            self.stats["total_vehicles"] = len(self.unique_vehicles)
            self.stats["working_only_vehicles"] = working_only_count

            # Store references for other methods
            self.daily_stats_by_vehicle = daily_stats_by_vehicle
            self.vin_to_vehicle_id = vin_to_vehicle_id
            self.vin_without_vehicle_id = vehicle_manager.vin_without_vehicle_id

            logger.info("Data aggregation phase completed successfully")

            # Continue with existing timeline processing logic
            logger.info("Running timeline analysis...")
            self.build_battery_timelines()

            logger.info("Battery Timeline completed")
            self.raw_battery_timelines = self.battery_timelines.copy()

            self.battery_timelines = self.preprocess_erstzulassung_candidates(
                self.battery_timelines
            )

            # Save intermediate files...
            erstzulassung_timeline = self.battery_timelines.copy()
            erstzulassung_timeline_df = pd.DataFrame(erstzulassung_timeline)
            erstzulassung_timeline_csv = "battery_lifecycle_timelines_erstzulassung.csv"
            erstzulassung_timeline_df.to_csv(erstzulassung_timeline_csv, index=False)
            logger.info(
                f"Saved erstzulassung timeline data to {erstzulassung_timeline_csv}"
            )

            if self.raw_battery_timelines:
                raw_timeline_df = pd.DataFrame(self.raw_battery_timelines)
                raw_timeline_csv = "battery_lifecycle_timelines_raw.csv"
                raw_timeline_df.to_csv(raw_timeline_csv, index=False)
                logger.info(f"Saved raw timeline data to {raw_timeline_csv}")

            # logger.info("Validating timelines before stitching...")
            # self.validate_timelines(self.battery_timelines)

            self.battery_timelines, self.conflicts = self.stitch_vehicle_timelines(
                self.battery_timelines,
                self.vehicle_info,
                self.daily_stats_by_vehicle,
                self.vin_to_vehicle_id,
                self.conflicts,
            )
            # self.battery_timelines = self.limit_concurrent_batteries_per_vin(
            #     self.battery_timelines
            # )
            post_activity_timeline = self.battery_timelines.copy()
            post_activity_timeline_df = pd.DataFrame(post_activity_timeline)
            post_activity_timeline_csv = "battery_lifecycle_timelines_post_activity.csv"
            post_activity_timeline_df.to_csv(post_activity_timeline_csv, index=False)

            self.battery_timelines = self.resolve_battery_timelines(
                self.battery_timelines
            )
            self.validate_timelines(self.battery_timelines)
            logger.info(
                "Battery Timeline completed! Proceed to resolve battery conflicts..."
            )
            # self.battery_timelines = self.resolve_battery_conflicts(
            #     self.battery_timelines,
            #     self.conflicts,
            #     self.vehicle_info,
            # )

            # self.validate_timelines(self.battery_timelines)
            csv_file, stats_file = self.generate_outputs()

            logger.info("Battery timeline analysis completed successfully!")
            if csv_file:
                logger.info(f"Timeline data saved to: {csv_file}")
            logger.info(f"Statistics saved to: {stats_file}")

            logger.info("Enhanced timeline analysis completed!")
            logger.info(f"Timeline intervals: {len(self.battery_timelines)}")
            logger.info(f"Batteries with timelines: {len(self.battery_processors)}")

            # Generate dual-battery report
            dual_battery_report = self.generate_dual_battery_report()
            logger.info(
                f"Dual-battery vehicles found: {dual_battery_report['total_dual_battery_vehicles']}"
            )

            # Append dual report to stats file
            with open(stats_file, "a") as f:
                f.write(f"\nDual-Battery Configuration:\n")
                f.write(
                    f"  Vehicles with 2 active batteries: {dual_battery_report['total_dual_battery_vehicles']}\n"
                )
                f.write(
                    f"  VINs: {', '.join(dual_battery_report['dual_battery_vins'][:10])}{'...' if len(dual_battery_report['dual_battery_vins']) > 10 else ''}\n"
                )

            return csv_file, stats_file

        except Exception as e:
            logger.error(f"Error during processing: {e}")
            raise

    def generate_dual_battery_report(self):
        """Generate report on dual-battery configurations"""
        dual_battery_vins = []

        # Group intervals by VIN
        by_vin = defaultdict(list)
        for iv in self.battery_timelines:
            by_vin[iv["vin"]].append(iv)

        for vin, intervals in by_vin.items():
            # Count currently active batteries (interval_end is None)
            concurrent = len([iv for iv in intervals if iv["interval_end"] is None])
            if concurrent == 2:
                dual_battery_vins.append(vin)

        return {
            "total_dual_battery_vehicles": len(dual_battery_vins),
            "dual_battery_vins": dual_battery_vins,
        }


if __name__ == "__main__":
    import sys

    # Run timeline analysis only (age calculations are commented out)
    logger.info("Running battery timeline analysis...")
    timeline_generator = BatteryTimelineGenerator()
    timeline_generator.run()
