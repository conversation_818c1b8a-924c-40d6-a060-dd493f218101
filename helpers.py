import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
from sqlalchemy import create_engine, text
import logging
from typing import Dict, List, Tuple, Optional, Set, Any, TypedDict

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("generate_battery_timeline.log"),
    ],
)
logger = logging.getLogger(__name__)

class BatteryEvent(TypedDict):
    """
    Structure of a battery event as processed from repair data.

    Fields:
    - vin: Vehicle identification number where event occurred
    - date: Date when the event happened (datetime.date object)
    - column: Event type - "old" (removal), "new" (installation), or None (working-only vehicle)
    - event_id: Unique identifier for the repair record (DataFrame index or None for working-only)
    - row_data: Complete repair record data (pandas Series or None for working-only)
    """

    vin: str
    date: datetime.date  # Note: this is datetime.date, not datetime.datetime
    column: Optional[str]  # "old", "new", or None
    event_id: Optional[int]  # DataFrame index or None
    row_data: Optional[Any]  # pandas Series or None


class BatteryInterval(TypedDict):
    """
    Structure of a battery lifecycle interval (one residence period in a vehicle).

    Fields:
    - battery_id: Unique battery identifier
    - vin: Vehicle where battery resided during this interval
    - interval_start: Installation date (datetime.date)
    - interval_end: Removal date (datetime.date or None if still installed)
    - interval_type: Classification - "original", "replacement", "swap", "unknown"
    - source_event_ids: List of repair event IDs that created/closed this interval
    - confidence: Data quality score (0.0-1.0)
    - notes: Additional context or error information
    """

    battery_id: str
    vin: str
    interval_start: Optional[datetime.date]  # None for unknown start
    interval_end: Optional[datetime.date]  # None for currently installed
    interval_type: (
        str  # "based  from start and end, it can be record_start_infer_end,...
    )
    lifecycle_stage: str  # initial, Current, Orphaned, Ghost, Intermediate (Battery installed and later removed)
    source_event_ids: List[Optional[int]]  # Event IDs that created this interval
    confidence: float  # 0.0-1.0 data quality score
    notes: str  # Additional context


class BatteryProcessor:
    """
    Processes lifecycle events for a single battery using state machine approach.

    This class tracks the complete lifecycle of one battery through multiple vehicles.
    It processes chronologically ordered events (installations/removals) and maintains:
    - One open_interval at most (current installation)
    - Multiple completed_intervals (past residence periods)

    State transitions:
    - "new" event: Installation → opens new interval (closes previous if exists)
    - "old" event: Removal → closes current interval
    - None event: Working-only vehicle → creates interval from erstzulassung
    """

    def __init__(self, battery_id: str):
        self.today = datetime.now().date()
        self.battery_id = battery_id
        self.open_interval = None  # Current active interval (if any)
        self.completed_intervals: List[BatteryInterval] = []  # List of closed intervals
        self.appearance_counter = 0  # Track appearance order for classification

    def process_event(self, event: BatteryEvent) -> None:
        """
        Process a single battery event (installation or removal).

        Args:
            event: BatteryEvent containing vin, date, column, event_id, and row_data
        """
        self.appearance_counter += 1

        if event["column"] == "new":  # Installation event
            self._handle_installation(event)
        elif event["column"] == "old":  # Removal event
            self._handle_removal(event)
        elif event["column"] is None:  # Working-only vehicle
            self._handle_working_only(event)

    def _handle_installation(self, event: BatteryEvent) -> None:
        """
        Args:
            event: Installation event with column="new"
        """
        if self.open_interval:
            # Close previous interval (assume removal before installation)
            self._close_interval(
                event["date"],
                confidence=0.7,
                note="Missing removal - Auto-closed before new installation",
                interval_type="record_start_infer_end",
            )

        self.open_interval = {
            "vin": event["vin"],
            "start": event["date"],
            "open_src": event.get("event_id"),
            "interval_type": "record_start_ongoing",
            "confidence": 0.85,
            "battery_id": self.battery_id,
            "notes": "",
        }

    def _handle_removal(self, event: BatteryEvent) -> None:
        """
        Args:
            event: Removal event with column="old"
        """
        if self.open_interval and self.open_interval["vin"] == event["vin"]:
            self._close_interval(
                event["date"],
                confidence=0.95,
                note="",
                interval_type="record_start_record_end",
            )
        elif self.open_interval:
            # Removal from different vehicle - possible data error
            self._close_interval(
                event["date"],
                confidence=0.6,
                note=f"Removal event from {event['vin']} but was installed in {self.open_interval['vin']} on {self.open_interval['start']}",
                interval_type="record_start_record_end_mismatch",
            )
        else:
            self._handle_orphaned_removal(event)

    def _handle_working_only(self, event: BatteryEvent) -> None:
        """
        Handle battery from working-only vehicle (no repair history).

        Args:
            event: Working-only event with column=None
        """
        if not self.open_interval:
            # Create open interval starting from erstzulassung
            self.open_interval = {
                "vin": event["vin"],
                "start": event["date"],
                "open_src": None,
                "battery_id": self.battery_id,
                "notes": "Working-only vehicle - Start date from erstzulassung",
                "confidence": 0.85,
                "interval_type": "estimate_start_ongoing",
            }

    def _handle_orphaned_removal(self, event: BatteryEvent) -> None:
        """
        Handle removal event without corresponding installation.
        """
        is_first_appearance = self.appearance_counter == 1

        # first time seeing this battery in "old" column, without prior installation, high chance that this battery is first installed into this vehicle
        if is_first_appearance:
            interval = {
                "battery_id": self.battery_id,
                "vin": event["vin"],
                "interval_start": None,
                "interval_end": event["date"],
                "interval_type": "infer_start_record_end",
                "source_event_ids": [event.get("event_id")],
                "confidence": 0.8,
                "notes": "First appearance - high confidence for erstzulassung start",
                "erstzulassung_candidate": True,
            }
        else:
            interval = {
                "battery_id": self.battery_id,
                "vin": event["vin"],
                "interval_start": None,
                "interval_end": event["date"],
                "interval_type": "orphaned_removal",
                "source_event_ids": [event.get("event_id")],
                "confidence": 0.5,
                "interval_type": "orphaned_removal",
                "notes": "Removal without installation record",
                "erstzulassung_candidate": False,
            }

        self.completed_intervals.append(interval)

    def _close_interval(
        self,
        end_date: datetime,
        confidence: float = 0.95,
        note: str = "",
        interval_type: str = "",
    ) -> None:
        """Close the currently opened-interval."""
        if not self.open_interval:
            return

        interval = {
            "battery_id": self.battery_id,
            "vin": self.open_interval["vin"],
            "interval_start": self.open_interval["start"],
            "interval_end": end_date,
            "interval_type": interval_type or self.open_interval["interval_type"],
            "source_event_ids": [self.open_interval["open_src"]],
            "confidence": confidence,
            "notes": note,
        }
        self.completed_intervals.append(interval)
        self.open_interval = None

    def finalize(self) -> List[BatteryInterval]:
        """
        Finalize processing and return all intervals (including open ones).

        Returns:
            List[BatteryInterval]: Complete list of battery residence intervals
        """
        intervals = self.completed_intervals.copy()

        # Add open interval as ongoing
        if self.open_interval:
            interval: BatteryInterval = {
                "battery_id": self.battery_id,
                "vin": self.open_interval["vin"],
                "interval_start": self.open_interval["start"],
                "interval_end": self.today,  # fill today to differentiate interval that has missing end
                "interval_type": self.open_interval["interval_type"],
                "source_event_ids": [self.open_interval["open_src"]],
                "notes": self.open_interval["notes"],
                "confidence": self.open_interval["confidence"],
            }
            intervals.append(interval)

        return intervals


class DataLoader:
    """
    Handles all data loading, cleaning, database connections, and pre-indexing.
    Focused on I/O operations and data preparation.
    """

    def __init__(self):
        self.today = datetime.now().date()
        self.errors = []

    def initialize_database_connection(self) -> create_engine:
        """Initialize and return database connection engine."""
        host = os.getenv("DB_HOST", "localhost")
        port = os.getenv("DB_PORT", "6543")
        database = os.getenv("DB_NAME", "LeitwartenDB")
        user = os.getenv("DB_USER", "datadump")
        password = os.getenv("DB_PASSWORD", "pAUjuLftyHURa5Ra")
        db_connection_string = (
            f"postgresql://{user}:{password}@{host}:{port}/{database}"
        )
        try:
            engine = create_engine(db_connection_string)
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
                logger.info("✅ Database connection established successfully")
            return engine
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            logger.error(
                "Pipeline requires PostgreSQL connection for activity validation"
            )
            logger.error(f"Connection string: {db_connection_string}")
            logger.error("Please ensure PostgreSQL server is running and accessible")
            raise ConnectionError(f"Required database connection failed: {e}")

    def load_vin_mappings(self, engine) -> Dict[str, int]:
        """Load VIN to vehicle_id mapping from database."""
        if not engine:
            logger.warning(
                "No database connection - cannot load VIN to vehicle_id mapping"
            )
            raise ConnectionError("Database engine required")

        try:
            mapping_query = """
            SELECT vin, vehicle_id
            FROM public.vehicles 
            WHERE vin IS NOT NULL
            """
            mapping_df = pd.read_sql(mapping_query, engine)
            logger.info(
                f"Loaded VIN mapping for {len(mapping_df):,} vehicles from database"
            )

            # Build VIN to vehicle_id mapping
            vin_to_vehicle_id = {}
            for _, row in mapping_df.iterrows():
                vin = row["vin"]
                if pd.notna(vin):
                    vin_to_vehicle_id[vin] = row["vehicle_id"]

            logger.info(
                f"Built VIN to vehicle_id mapping for {len(vin_to_vehicle_id)} vehicles"
            )
            return vin_to_vehicle_id

        except Exception as e:
            logger.error(f"Failed to load VIN to vehicle_id mapping: {e}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            raise

    def load_data(
        self,
    ) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Load all data files and return DataFrames."""
        logger.info("Loading data files...")

        try:
            # Load HV repair data
            logger.info("Loading HV repair data...")
            hv_repair_df = pd.read_csv("hv_repair_2025-06-02b.csv")
            logger.info(f"Loaded {len(hv_repair_df)} HV repair records")

            # Load working vehicles data
            logger.info("Loading working vehicles data and daily stats...")
            working_vehicles_df = pd.read_csv(
                "comparison_results/working_matching_vehicles.csv"
            )
            working_unique_df = pd.read_csv(
                "comparison_results/working_unique_vehicles.csv"
            )
            daily_stats_df = pd.read_csv(
                "daily_stats.csv",
                dtype={"vehicle_id": "int", "km_start": "float", "km_end": "float"},
                parse_dates=["date"],
            )

            logger.info(f"Loaded {len(working_vehicles_df)} matching vehicles")
            logger.info(f"Loaded {len(working_unique_df)} unique vehicles")
            logger.info(f"Loaded {len(daily_stats_df)} daily stats records")

            return hv_repair_df, working_vehicles_df, working_unique_df, daily_stats_df

        except Exception as e:
            error_msg = f"Error loading data files: {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            raise

    def clean_data(
        self, hv_repair_df: pd.DataFrame, working_vehicles_df: pd.DataFrame
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Clean and prepare data for processing."""
        logger.info("Cleaning data...")

        try:
            # Clean HV repair data
            hv_repair_df["created"] = pd.to_datetime(
                hv_repair_df["created"], errors="coerce"
            )
            hv_repair_df["battery_changed"] = hv_repair_df["battery_changed"].replace(
                "--", None
            )
            hv_repair_df["battery_changed"] = pd.to_datetime(
                hv_repair_df["battery_changed"], errors="coerce"
            )

            # Create effective date (battery_changed if available, otherwise created)
            hv_repair_df["effective_date"] = hv_repair_df["battery_changed"].fillna(
                hv_repair_df["created"]
            )

            # Clean battery IDs
            for col in ["battery_id_old", "battery_id_new"]:
                hv_repair_df[col] = hv_repair_df[col].astype(str)
                hv_repair_df[col] = hv_repair_df[col].replace(
                    ["nan", "", " ", "None"], None
                )

            # Clean working vehicles data
            working_vehicles_df["erstzulassung"] = pd.to_datetime(
                working_vehicles_df["erstzulassung"], errors="coerce"
            )

            for col in ["master", "slave"]:
                if col in working_vehicles_df.columns:
                    working_vehicles_df[col] = working_vehicles_df[col].astype(str)
                    working_vehicles_df[col] = working_vehicles_df[col].replace(
                        ["nan", "", " ", "None"], None
                    )

            # Filter valid records
            hv_repair_df = hv_repair_df.dropna(subset=["vin", "effective_date"])
            working_vehicles_df = working_vehicles_df.dropna(subset=["vin"])

            logger.info(
                f"After cleaning: {len(hv_repair_df)} repair records, {len(working_vehicles_df)} working vehicles"
            )

            return hv_repair_df, working_vehicles_df

        except Exception as e:
            error_msg = f"Error cleaning data: {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            raise

    def pre_index_daily_stats(
        self, daily_stats_df: pd.DataFrame
    ) -> Dict[int, pd.DataFrame]:
        """Group and sort daily stats by vehicle_id for fast lookup."""
        logger.info("Pre-indexing daily stats by vehicle_id for fast lookup...")

        try:
            daily_stats_by_vehicle = {}
            for vehicle_id, group in daily_stats_df.groupby("vehicle_id"):
                # Sort by date for each vehicle
                vehicle_data = group.sort_values("date").copy()
                daily_stats_by_vehicle[vehicle_id] = vehicle_data

            logger.info(
                f"Pre-indexed daily stats for {len(daily_stats_by_vehicle):,} vehicles"
            )
            logger.info("Memory optimization: daily stats DataFrame can now be cleared")

            return daily_stats_by_vehicle

        except Exception as e:
            error_msg = f"Error pre-indexing daily stats: {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            raise
